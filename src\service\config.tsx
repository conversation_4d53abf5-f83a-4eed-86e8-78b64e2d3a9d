import { Platform } from "react-native"

// For development with local server
export const BASE_URL = Platform.OS==='android' ? 'http://********:3000/api': 'http://localhost:3000/api'
export const SOCKET_URL =  Platform.OS==='android' ? 'http://********:3000': 'http://localhost:3000'

// Google Maps API key - replace with your actual API key
export const GOOGLE_MAP_API = "AIzaSyB3-FJ4nRfZz19GUndm70CKxH94-rZ9uKo"
export const BRANCH_ID ='branch_123456789'

// USE YOUR NETWORK IP OR HOSTED URL
// export const BASE_URL = 'http://***********:3000/api'
// export const SOCKET_URL = 'http://***********:3000'

