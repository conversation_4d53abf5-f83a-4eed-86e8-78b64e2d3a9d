@echo off
echo ===================================================
echo Java 17 Configuration for React Native
echo ===================================================
echo.

echo Looking for Java 17 installation...

REM Check common installation paths
if exist "C:\Program Files\Eclipse Adoptium\jdk-**********-hotspot" (
    set "JAVA17_PATH=C:\Program Files\Eclipse Adoptium\jdk-**********-hotspot"
    goto :configure
)

REM Check for any Java 17 version in Eclipse Adoptium
for /d %%i in ("C:\Program Files\Eclipse Adoptium\jdk-17*") do (
    set "JAVA17_PATH=%%i"
    goto :configure
)

REM Check Program Files\Java
for /d %%i in ("C:\Program Files\Java\jdk-17*") do (
    set "JAVA17_PATH=%%i"
    goto :configure
)

echo Java 17 not found in common locations.
echo Please make sure you have installed Java 17 from:
echo https://adoptium.net/temurin/releases/?version=17
echo.
echo After installation, run this script again.
pause
exit /b 1

:configure
echo Found Java 17 at: %JAVA17_PATH%
echo.

echo Updating gradle.properties...
echo.

REM Create the gradle.properties line
set "GRADLE_JAVA_HOME=org.gradle.java.home=%JAVA17_PATH:\=\\%"

echo Adding Java 17 configuration to android\gradle.properties
echo %GRADLE_JAVA_HOME% >> android\gradle.properties

echo.
echo ===================================================
echo Configuration complete!
echo.
echo Java 17 path configured in gradle.properties
echo You can now try: npm run android
echo ===================================================
pause
