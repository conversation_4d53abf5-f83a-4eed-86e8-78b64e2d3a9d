# Android Development Environment Setup Guide

This guide will help you set up your Android development environment for React Native development.

## Prerequisites

- Windows 10 or later
- Administrator access to your computer
- At least 8GB of RAM (16GB recommended)
- At least 10GB of free disk space

## Step 1: Install Android Studio

1. Download Android Studio from the official website:
   [Download Android Studio](https://developer.android.com/studio)

2. Run the installer and follow the installation wizard.

3. During installation, make sure to select the following components:
   - Android SDK
   - Android SDK Platform
   - Android Virtual Device (AVD)

4. Complete the installation and launch Android Studio.

## Step 2: Install Required SDK Components

1. In Android Studio, go to Tools > SDK Manager.

2. In the SDK Platforms tab, select:
   - Android 14 (API Level 34) or the version you want to target

3. In the SDK Tools tab, select:
   - Android SDK Build-Tools
   - Android Emulator
   - Android SDK Platform-Tools
   - Google Play services

4. Click "Apply" to install the selected components.

## Step 3: Set Environment Variables

1. Find the location of your Android SDK:
   - In Android Studio, go to File > Settings > Appearance & Behavior > System Settings > Android SDK
   - Note the "Android SDK Location" path (e.g., C:\Users\<USER>\AppData\Local\Android\Sdk)

2. Set up environment variables:
   - Right-click on "This PC" and select "Properties"
   - Click on "Advanced system settings"
   - Click on "Environment Variables"
   - Under "System variables", click "New" and add:
     - Variable name: ANDROID_HOME
     - Variable value: Your Android SDK path (e.g., C:\Users\<USER>\AppData\Local\Android\Sdk)
   
   - Under "System variables", find the "Path" variable and click "Edit"
   - Click "New" and add the following paths:
     ```
     %ANDROID_HOME%\platform-tools
     %ANDROID_HOME%\emulator
     ```

3. Click "OK" to save the changes.

4. Restart your computer to apply the changes.

## Step 4: Create an Android Virtual Device (AVD)

1. In Android Studio, go to Tools > Device Manager.

2. Click on "Create Device".

3. Select a phone device (e.g., Pixel 5).

4. Select a system image (e.g., Android 14 with Google Play).

5. Give your AVD a name (e.g., "Pixel5_API34").

6. Click "Finish" to create the AVD.

## Step 5: Configure Your React Native Project

1. Create a `local.properties` file in the `android` folder of your project with the following content:
   ```
   sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk
   ```
   Replace `YourUsername` with your Windows username and adjust the path if your SDK is installed elsewhere.

## Step 6: Start the Emulator

You can start the emulator using one of the following methods:

### Method 1: Using Android Studio

1. In Android Studio, go to Tools > Device Manager.
2. Click the play button next to your AVD.

### Method 2: Using Command Line

1. Open Command Prompt.
2. Run the following command:
   ```
   %ANDROID_HOME%\emulator\emulator.exe -avd YourAVDName
   ```
   Replace `YourAVDName` with the name of your AVD.

### Method 3: Using the Provided Script

1. Run the `run-emulator.bat` script:
   ```
   run-emulator.bat "C:\Users\<USER>\AppData\Local\Android\Sdk"
   ```
   Replace `YourUsername` with your Windows username.

## Step 7: Run Your React Native App

Once the emulator is running, you can start your React Native app:

```
npm run android
```

## Troubleshooting

### Error: "Error fetching your Android emulators! Make sure your path is correct."

1. Make sure the Android SDK path in your `local.properties` file is correct.
2. Make sure the environment variables are set correctly.
3. Make sure you have created at least one AVD.
4. Try running the emulator list command:
   ```
   %ANDROID_HOME%\emulator\emulator.exe -list-avds
   ```

### Error: "No connected devices!"

1. Make sure the emulator is running before executing `npm run android`.
2. Try restarting the emulator.
3. Check if ADB can detect the emulator:
   ```
   %ANDROID_HOME%\platform-tools\adb.exe devices
   ```

### Error: "Failed to install the app"

1. Make sure the emulator is fully loaded before running `npm run android`.
2. Try clearing the app data:
   ```
   %ANDROID_HOME%\platform-tools\adb.exe shell pm clear com.your.app.package
   ```
3. Try uninstalling the app:
   ```
   %ANDROID_HOME%\platform-tools\adb.exe uninstall com.your.app.package
   ```

## Additional Resources

- [React Native Environment Setup](https://reactnative.dev/docs/environment-setup)
- [Android Studio Documentation](https://developer.android.com/studio/intro)
- [Android Emulator Documentation](https://developer.android.com/studio/run/emulator)
