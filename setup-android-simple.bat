@echo off
setlocal enabledelayedexpansion
echo ===================================================
echo Android SDK and Emulator Setup Script (Simple)
echo ===================================================
echo(

REM Try to find Android SDK in common locations
echo Searching for Android SDK in common locations...

REM Check each location individually
if exist "C:\Users\<USER>\AppData\Local\Android\Sdk" (
    echo Found Android SDK at: C:\Users\<USER>\AppData\Local\Android\Sdk
    set "ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk"
    goto :found
)

if exist "C:\Program Files\Android\Sdk" (
    echo Found Android SDK at: C:\Program Files\Android\Sdk
    set "ANDROID_HOME=C:\Program Files\Android\Sdk"
    goto :found
)

if exist "C:\Program Files (x86)\Android\Sdk" (
    echo Found Android SDK at: C:\Program Files (x86)\Android\Sdk
    set "ANDROID_HOME=C:\Program Files (x86)\Android\Sdk"
    goto :found
)

if exist "%LOCALAPPDATA%\Android\Sdk" (
    echo Found Android SDK at: %LOCALAPPDATA%\Android\Sdk
    set "ANDROID_HOME=%LOCALAPPDATA%\Android\Sdk"
    goto :found
)

echo Android SDK not found in common locations.
echo Please enter the full path to your Android SDK:
set /p ANDROID_HOME=

:found
echo Using Android SDK at: %ANDROID_HOME%
echo:

REM Create local.properties file
echo Creating local.properties file...
if not exist android mkdir android
echo sdk.dir=%ANDROID_HOME% > android\local.properties
echo Created android\local.properties with SDK path
echo:

REM Check if platform-tools exist (for adb)
echo Checking for Android platform tools...
if exist "%ANDROID_HOME%\platform-tools\adb.exe" (
    echo Found ADB at: %ANDROID_HOME%\platform-tools\adb.exe
) else (
    echo ADB not found. You may need to install platform-tools through Android Studio.
)
echo(

REM Check if emulator exists
if exist "%ANDROID_HOME%\emulator\emulator.exe" (
    echo Found emulator at: %ANDROID_HOME%\emulator\emulator.exe
    echo:

    echo Listing available Android Virtual Devices (AVDs):
    "%ANDROID_HOME%\emulator\emulator.exe" -list-avds
    echo:

    echo You can start your emulator with:
    echo "%ANDROID_HOME%\emulator\emulator.exe" -avd [AVD_NAME]
    echo:
    echo For example, to start the Pixel_9_Pro AVD:
    echo "%ANDROID_HOME%\emulator\emulator.exe" -avd Pixel_9_Pro
    echo:

) else (
    echo Emulator not found at: %ANDROID_HOME%\emulator\emulator.exe
    echo Please install Android Studio and the Android SDK.
    echo You can download it from: https://developer.android.com/studio
)

echo:
echo ===================================================
echo Setup complete!
echo:
echo Your Android SDK path has been configured in android\local.properties
echo You can now run "npm run android" to start your app.
echo Make sure to start an emulator first if you want to test on an emulator.
echo ===================================================
echo:
pause
