import axios from 'axios'
import { BASE_URL } from './config'
import { tokenStorage } from '@state/storage'
import { useAuthStore } from '@state/authStore'
import { resetAndNavigate } from '@utils/NavigationUtils'
import { appAxios } from './apiInterceptors'

export const customerLogin = async(phone:string)=>{
    try {
        console.log(`Sending login request to ${BASE_URL}/customer/login with phone:`, phone);
        const response = await axios.post(`${BASE_URL}/customer/login`,{phone})

        if (!response.data || !response.data.accessToken) {
            console.error('Invalid response from server:', response.data);
            throw new Error('Invalid response from server');
        }

        const { accessToken, refreshToken, customer } = response.data
        console.log('Login successful, storing tokens and user data');

        tokenStorage.set("accessToken", accessToken)
        tokenStorage.set("refreshToken", refreshToken)

        const {setUser} = useAuthStore.getState()
        setUser(customer)

        return customer;
    } catch (error: any) {
        console.error("Login Error:", error?.response?.data || error.message || error);
        throw error;
    }
}

export const deliveryLogin = async(email: string, password: string)=>{
    try {
        console.log(`Sending delivery login request to ${BASE_URL}/delivery/login`);
        const response = await axios.post(`${BASE_URL}/delivery/login`, { email, password })

        if (!response.data || !response.data.accessToken) {
            console.error('Invalid response from server:', response.data);
            throw new Error('Invalid response from server');
        }

        const { accessToken, refreshToken, deliveryPartner } = response.data
        console.log('Delivery login successful, storing tokens and user data');

        tokenStorage.set("accessToken", accessToken)
        tokenStorage.set("refreshToken", refreshToken)

        const {setUser} = useAuthStore.getState()
        setUser(deliveryPartner)

        return deliveryPartner;
    } catch (error: any) {
        console.error("Delivery Login Error:", error?.response?.data || error.message || error);
        throw error;
    }
}

export const refresh_tokens = async () => {
    try {
        const refreshToken = tokenStorage.getString('refreshToken')
        if (!refreshToken) {
            console.error('No refresh token found');
            throw new Error('No refresh token found');
        }

        console.log(`Sending refresh token request to ${BASE_URL}/refresh-token`);
        const response = await axios.post(`${BASE_URL}/refresh-token`, {
            refreshToken
        })

        if (!response.data || !response.data.accessToken) {
            console.error('Invalid response from refresh token endpoint:', response.data);
            throw new Error('Invalid response from server');
        }

        const new_access_token = response.data.accessToken
        const new_refresh_token = response.data.refreshToken

        console.log('Token refresh successful, storing new tokens');
        tokenStorage.set('accessToken', new_access_token)
        tokenStorage.set('refreshToken', new_refresh_token)
        return new_access_token;
    } catch (error: any) {
        console.error("REFRESH TOKEN ERROR:", error?.response?.data || error.message || error);
        tokenStorage.clearAll()
        resetAndNavigate("CustomerLogin")
        throw error;
    }
}

export const refetchUser = async (setUser: any) => {
    try {
        console.log('Fetching user data');
        const response = await appAxios.get(`/user`)

        if (!response.data || !response.data.user) {
            console.error('Invalid user data response:', response.data);
            throw new Error('Invalid user data response');
        }

        console.log('User data fetched successfully');
        setUser(response.data.user)
        return response.data.user;
    } catch (error: any) {
        console.error("User fetch error:", error?.response?.data || error.message || error);
        throw error;
    }
}

export const updateUserLocation = async (data: any, setUser: any) => {
    try {
        console.log('Updating user location with data:', data);
        const response = await appAxios.patch(`/user`, data)

        if (!response.data) {
            console.error('Invalid response from location update:', response);
            throw new Error('Failed to update location');
        }

        console.log('Location updated successfully, refreshing user data');
        await refetchUser(setUser)
        return response.data;
    } catch (error: any) {
        console.error("Update user location error:", error?.response?.data || error.message || error);
        // Don't throw here to prevent app crashes during location updates
        return null;
    }
}
