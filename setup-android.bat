@echo off
setlocal enabledelayedexpansion
echo ===================================================
echo Android SDK and Emulator Setup Script
echo ===================================================
echo.

REM Try to find Android SDK in common locations
echo Searching for Android SDK in common locations...

REM Check each location individually
if exist "C:\Users\<USER>\AppData\Local\Android\Sdk" (
    echo Found Android SDK at: C:\Users\<USER>\AppData\Local\Android\Sdk
    set "ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk"
    goto :found
)

if exist "C:\Program Files\Android\Sdk" (
    echo Found Android SDK at: C:\Program Files\Android\Sdk
    set "ANDROID_HOME=C:\Program Files\Android\Sdk"
    goto :found
)

if exist "C:\Program Files (x86)\Android\Sdk" (
    echo Found Android SDK at: C:\Program Files (x86)\Android\Sdk
    set "ANDROID_HOME=C:\Program Files (x86)\Android\Sdk"
    goto :found
)

if exist "%LOCALAPPDATA%\Android\Sdk" (
    echo Found Android SDK at: %LOCALAPPDATA%\Android\Sdk
    set "ANDROID_HOME=%LOCALAPPDATA%\Android\Sdk"
    goto :found
)

echo Android SDK not found in common locations.
echo Please enter the full path to your Android SDK:
set /p ANDROID_HOME=

:found
echo Using Android SDK at: %ANDROID_HOME%
echo.

REM Create local.properties file
echo Creating local.properties file...
if not exist android mkdir android
echo sdk.dir=%ANDROID_HOME% > android\local.properties
echo Created android\local.properties with SDK path
echo.

REM Check if platform-tools exist (for adb)
echo Checking for Android platform tools...
if exist "%ANDROID_HOME%\platform-tools\adb.exe" (
    echo Found ADB at: %ANDROID_HOME%\platform-tools\adb.exe
) else (
    echo ADB not found. You may need to install platform-tools through Android Studio.
)
echo.

REM Check if emulator exists
if exist "%ANDROID_HOME%\emulator\emulator.exe" (
    echo Found emulator at: %ANDROID_HOME%\emulator\emulator.exe
    echo.

    echo Listing available Android Virtual Devices (AVDs):
    "%ANDROID_HOME%\emulator\emulator.exe" -list-avds
    echo.

    echo Available AVDs found! You can start your emulator with:
    echo "%ANDROID_HOME%\emulator\emulator.exe" -avd [AVD_NAME]
    echo.
    echo For example, to start the first AVD listed above:
    for /f "tokens=*" %%a in ('"%ANDROID_HOME%\emulator\emulator.exe" -list-avds') do (
        echo "%ANDROID_HOME%\emulator\emulator.exe" -avd %%a
        goto :first_avd_shown
    )
    :first_avd_shown
    echo.

    set /p START_EMU=Do you want to start an emulator now? (y/n):
    if /i "%START_EMU%"=="y" (
        echo.
        echo Enter the name of the AVD you want to start:
        set /p AVD_NAME=
        if not "!AVD_NAME!"=="" (
            echo Starting emulator with AVD: !AVD_NAME!
            start "" "%ANDROID_HOME%\emulator\emulator.exe" -avd !AVD_NAME!
            echo Emulator is starting in a new window.
        )
    )
) else (
    echo Emulator not found at: %ANDROID_HOME%\emulator\emulator.exe
    echo Please install Android Studio and the Android SDK.
    echo You can download it from: https://developer.android.com/studio
)

:end
echo.
echo ===================================================
echo Setup complete!
echo.
echo You can now run "npm run android" to start your app.
echo If you encounter any issues, make sure your emulator is running.
echo ===================================================
