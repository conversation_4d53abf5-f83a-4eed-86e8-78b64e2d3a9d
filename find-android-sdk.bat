@echo off
echo Finding Android SDK location...

REM Check common locations
if exist "C:\Users\<USER>\AppData\Local\Android\Sdk" (
    echo Found Android SDK at: C:\Users\<USER>\AppData\Local\Android\Sdk
    set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
    goto :found
)

if exist "C:\Program Files\Android\Sdk" (
    echo Found Android SDK at: C:\Program Files\Android\Sdk
    set ANDROID_HOME=C:\Program Files\Android\Sdk
    goto :found
)

if exist "C:\Program Files (x86)\Android\Sdk" (
    echo Found Android SDK at: C:\Program Files (x86)\Android\Sdk
    set ANDROID_HOME=C:\Program Files (x86)\Android\Sdk
    goto :found
)

if exist "%LOCALAPPDATA%\Android\Sdk" (
    echo Found Android SDK at: %LOCALAPPDATA%\Android\Sdk
    set ANDROID_HOME=%LOCALAPPDATA%\Android\Sdk
    goto :found
)

echo Android SDK not found in common locations.
echo Please enter the full path to your Android SDK:
set /p ANDROID_HOME=

:found
echo Using Android SDK at: %ANDROID_HOME%

REM Create local.properties file
echo Creating local.properties file...
echo sdk.dir=%ANDROID_HOME% > android\local.properties

REM Check if emulator exists
if exist "%ANDROID_HOME%\emulator\emulator.exe" (
    echo Found emulator at: %ANDROID_HOME%\emulator\emulator.exe
    
    echo Listing available Android Virtual Devices (AVDs):
    "%ANDROID_HOME%\emulator\emulator.exe" -list-avds
) else (
    echo Emulator not found at: %ANDROID_HOME%\emulator\emulator.exe
    echo Please make sure the Android SDK is properly installed.
)

echo.
echo Setup complete. You can now run "npm run android" to start your app.
pause
