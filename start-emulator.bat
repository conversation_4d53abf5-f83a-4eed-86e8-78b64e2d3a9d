@echo off
echo Starting Android Emulator...

REM Find Android SDK
if exist "C:\Users\<USER>\AppData\Local\Android\Sdk" (
    set "ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk"
) else (
    echo Android SDK not found. Please run quick-android-setup.bat first.
    pause
    exit /b 1
)

REM Check if emulator exists
if not exist "%ANDROID_HOME%\emulator\emulator.exe" (
    echo Emulator not found. Please install Android Studio.
    pause
    exit /b 1
)

REM List available AVDs
echo Available AVDs:
"%ANDROID_HOME%\emulator\emulator.exe" -list-avds

REM Start the Pixel_9_Pro emulator with Windows-specific fixes
echo.
echo Starting Pixel_9_Pro emulator with optimized settings...
echo This may take a few minutes for the first boot...

REM Use specific parameters to fix Windows display issues
start "" "%ANDROID_HOME%\emulator\emulator.exe" -avd Pixel_9_Pro -gpu swiftshader_indirect -no-snapshot-load -wipe-data

echo.
echo Emulator is starting with the following optimizations:
echo - GPU acceleration: swiftshader_indirect
echo - Fresh boot (no snapshot)
echo - Clean data
echo.
echo Wait for the emulator to fully load, then run: npm run android
echo If the emulator window doesn't appear, try Alt+Tab to find it.
pause
