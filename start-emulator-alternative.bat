@echo off
echo Starting Android Emulator (Alternative Method)...

REM Find Android SDK
if exist "C:\Users\<USER>\AppData\Local\Android\Sdk" (
    set "ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk"
) else (
    echo Android SDK not found. Please run quick-android-setup.bat first.
    pause
    exit /b 1
)

REM Check if emulator exists
if not exist "%ANDROID_HOME%\emulator\emulator.exe" (
    echo Emulator not found. Please install Android Studio.
    pause
    exit /b 1
)

echo.
echo Trying alternative emulator startup with different GPU settings...
echo This method often works better on some Windows systems.
echo.

REM Try with software rendering (most compatible)
start "" "%ANDROID_HOME%\emulator\emulator.exe" -avd Pixel_9_Pro -gpu host -no-boot-anim -no-snapshot

echo.
echo Emulator starting with:
echo - GPU: host rendering
echo - No boot animation (faster startup)
echo - No snapshot loading
echo.
echo If this doesn't work, try running Android Studio and starting the emulator from there.
echo Go to: Tools > Device Manager > Click the Play button next to <PERSON><PERSON>l_9_Pro
pause
