import {View, Text, StyleSheet, Image, Alert, ActivityIndicator} from 'react-native';
import React, {FC, useEffect, useState} from 'react';
import {Colors, Fonts} from '@utils/Constants';
import Logo from '@assets/images/logo.jpeg';
import {screenHeight, screenWidth} from '@utils/Scaling';
import { resetAndNavigate} from '@utils/NavigationUtils';
import GeoLocation from '@react-native-community/geolocation';
import {useAuthStore} from '@state/authStore';
import {tokenStorage} from '@state/storage';
import {jwtDecode} from 'jwt-decode';
import {refetchUser, refresh_tokens} from '@service/authService';

GeoLocation.setRNConfiguration({
  skipPermissionRequests: false,
  authorizationLevel: 'always',
  enableBackgroundLocationUpdates: true,
  locationProvider: 'auto',
});

interface DecodedToken {
  exp: number;
}

const SplashScreen: FC = () => {
  const {user, setUser} = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const tokenCheck = async () => {
    try {
      const accessToken = tokenStorage.getString('accessToken');
      const refreshToken = tokenStorage.getString('refreshToken');

      if (!accessToken || !refreshToken) {
        console.log('No tokens found, redirecting to login');
        resetAndNavigate('CustomerLogin');
        return false;
      }

      const decodedAccessToken = jwtDecode<DecodedToken>(accessToken);
      const decodedRefreshToken = jwtDecode<DecodedToken>(refreshToken);
      const currentTime = Date.now() / 1000;

      if (decodedRefreshToken?.exp < currentTime) {
        console.log('Refresh token expired');
        resetAndNavigate('CustomerLogin');
        Alert.alert('Session Expired', 'Please login again');
        return false;
      }

      if (decodedAccessToken?.exp < currentTime) {
        console.log('Access token expired, refreshing...');
        try {
          await refresh_tokens();
          await refetchUser(setUser);
        } catch (error) {
          console.log('Token refresh error:', error);
          Alert.alert('There was an error refreshing token!');
          resetAndNavigate('CustomerLogin');
          return false;
        }
      }

      console.log('User role:', user?.role);
      if (user?.role === 'Customer') {
        resetAndNavigate('ProductDashboard');
      } else if (user?.role === 'Delivery') {
        resetAndNavigate('DeliveryDashboard');
      } else {
        // Default to customer login if role is undefined
        console.log('Unknown user role, defaulting to customer login');
        resetAndNavigate('CustomerLogin');
      }

      return true;
    } catch (err) {
      console.error('Token check error:', err);
      setError('Authentication error. Please login again.');
      resetAndNavigate('CustomerLogin');
      return false;
    }
  };

  useEffect(() => {
    const intialStartup = async () => {
      try {
        setLoading(true);
        GeoLocation.requestAuthorization();
        await tokenCheck();
      } catch (error) {
        console.error('Startup error:', error);
        setError('Failed to start the app. Please try again.');
        Alert.alert(
          'Sorry we need location service to give you better shopping experience',
        );
      } finally {
        setLoading(false);
      }
    };

    const timeoutId = setTimeout(intialStartup, 1000);
    return () => clearTimeout(timeoutId);
  }, []);

  if (error) {
    return (
      <View style={styles.container}>
        <Image source={Logo} style={styles.logoImage} />
        <Text style={styles.errorText}>{error}</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Image source={Logo} style={styles.logoImage} />
      {loading && <ActivityIndicator size="large" color="#fff" style={styles.loader} />}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.primary,
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoImage: {
    height: screenHeight * 0.4,
    width: screenWidth * 0.4,
    resizeMode: 'contain',
  },
  errorText: {
    color: '#fff',
    fontSize: 16,
    marginTop: 20,
    textAlign: 'center',
    fontFamily: Fonts.Medium,
    paddingHorizontal: 20,
  },
  loader: {
    marginTop: 20,
  },
});

export default SplashScreen;
