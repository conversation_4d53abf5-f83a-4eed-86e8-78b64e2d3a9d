@echo off
echo ===================================================
echo Android Build Fix Script
echo ===================================================
echo.

REM Set up Android SDK path
echo Setting up Android SDK path...
if exist "C:\Users\<USER>\AppData\Local\Android\Sdk" (
    set ANDROID_SDK=C:\Users\<USER>\AppData\Local\Android\Sdk
) else (
    echo Android SDK not found at the default location.
    echo Please enter the full path to your Android SDK:
    set /p ANDROID_SDK=
)

echo Using Android SDK at: %ANDROID_SDK%

REM Create local.properties file with proper path format
echo Creating local.properties file...
echo sdk.dir=%ANDROID_SDK:\=\\% > android\local.properties
echo Created android\local.properties with SDK path

REM Fix build.gradle files
echo Fixing build.gradle files...

REM Fix root build.gradle
echo Updating root build.gradle...
powershell -Command "(Get-Content android\build.gradle) -replace 'buildToolsVersion = \"35.0.0\"', 'buildToolsVersion = \"34.0.0\"' -replace 'compileSdkVersion = 35', 'compileSdkVersion = 34' -replace 'ndkVersion = \"27.1.12297006\"', 'ndkVersion = \"26.1.10909125\"' -replace 'kotlinVersion = \"2.0.21\"', 'kotlinVersion = \"1.9.22\"' | Set-Content android\build.gradle"

REM Fix dependencies in root build.gradle
powershell -Command "(Get-Content android\build.gradle) -replace 'classpath\(\"com.android.tools.build:gradle\"\)', 'classpath(\"com.android.tools.build:gradle:8.2.2\")' -replace 'classpath\(\"org.jetbrains.kotlin:kotlin-gradle-plugin\"\)', 'classpath(\"org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion\")' | Set-Content android\build.gradle"

REM Clean the project
echo Cleaning the project...
cd android && gradlew.bat clean && cd ..

echo.
echo ===================================================
echo Build fixes applied!
echo.
echo Now try running your app with:
echo npm run android
echo ===================================================
