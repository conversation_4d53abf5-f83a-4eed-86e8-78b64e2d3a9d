@echo off
echo ===================================================
echo Android Emulator Complete Removal Script
echo ===================================================
echo.
echo This script will remove:
echo - All Android Virtual Devices (AVDs)
echo - Emulator cache and data
echo - Emulator configuration files
echo - Emulator binaries from Android SDK
echo.
echo WARNING: This will delete all your existing AVDs!
echo You will need to recreate them after reinstalling.
echo.
set /p CONFIRM=Are you sure you want to continue? (y/n): 
if /i not "%CONFIRM%"=="y" (
    echo Operation cancelled.
    pause
    exit /b 0
)

echo.
echo Starting emulator removal process...

REM Kill any running emulator processes
echo Stopping any running emulator processes...
taskkill /F /IM emulator.exe 2>nul
taskkill /F /IM qemu-system-x86_64.exe 2>nul
taskkill /F /IM emulator-arm.exe 2>nul
taskkill /F /IM emulator-x86.exe 2>nul

REM Remove AVD files from user directory
echo.
echo Removing AVD files from user directory...
if exist "%USERPROFILE%\.android\avd" (
    echo Deleting: %USERPROFILE%\.android\avd
    rmdir /s /q "%USERPROFILE%\.android\avd"
    echo AVD directory removed.
) else (
    echo AVD directory not found.
)

REM Remove emulator cache
echo.
echo Removing emulator cache...
if exist "%USERPROFILE%\.android\cache" (
    echo Deleting: %USERPROFILE%\.android\cache
    rmdir /s /q "%USERPROFILE%\.android\cache"
    echo Cache directory removed.
) else (
    echo Cache directory not found.
)

REM Remove emulator logs
echo.
echo Removing emulator logs...
if exist "%USERPROFILE%\.android\logs" (
    echo Deleting: %USERPROFILE%\.android\logs
    rmdir /s /q "%USERPROFILE%\.android\logs"
    echo Logs directory removed.
) else (
    echo Logs directory not found.
)

REM Remove emulator temp files
echo.
echo Removing temporary emulator files...
if exist "%TEMP%\android-*" (
    for /d %%i in ("%TEMP%\android-*") do (
        echo Deleting: %%i
        rmdir /s /q "%%i" 2>nul
    )
)

REM Remove emulator from Android SDK
echo.
echo Removing emulator from Android SDK...
set "ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk"
if exist "%ANDROID_HOME%\emulator" (
    echo Deleting: %ANDROID_HOME%\emulator
    rmdir /s /q "%ANDROID_HOME%\emulator"
    echo Emulator SDK directory removed.
) else (
    echo Emulator SDK directory not found.
)

REM Remove system images
echo.
echo Removing system images...
if exist "%ANDROID_HOME%\system-images" (
    echo Deleting: %ANDROID_HOME%\system-images
    rmdir /s /q "%ANDROID_HOME%\system-images"
    echo System images removed.
) else (
    echo System images directory not found.
)

REM Clean up any remaining emulator-related files
echo.
echo Cleaning up remaining emulator files...
if exist "%USERPROFILE%\.android\emulator_console_auth_token" (
    del "%USERPROFILE%\.android\emulator_console_auth_token"
    echo Removed emulator auth token.
)

if exist "%USERPROFILE%\.android\emu-update-last-check.ini" (
    del "%USERPROFILE%\.android\emu-update-last-check.ini"
    echo Removed emulator update check file.
)

echo.
echo ===================================================
echo Emulator removal completed!
echo.
echo What was removed:
echo - All AVDs (Android Virtual Devices)
echo - Emulator binaries and tools
echo - System images
echo - Cache and temporary files
echo - Configuration files
echo.
echo Your Android SDK is still intact for building apps.
echo You can now manually install the emulator through:
echo 1. Android Studio (recommended)
echo 2. SDK Manager command line tools
echo ===================================================
echo.
pause
