@echo off
echo Android Emulator Launcher

REM Check if Android SDK path is provided as argument
if "%~1"=="" (
    echo Please provide the path to your Android SDK.
    echo Example: run-emulator.bat "C:\Users\<USER>\AppData\Local\Android\Sdk"
    echo.
    echo Or enter the path now:
    set /p ANDROID_SDK=
) else (
    set ANDROID_SDK=%~1
)

REM Check if the SDK path exists
if not exist "%ANDROID_SDK%" (
    echo The specified Android SDK path does not exist: %ANDROID_SDK%
    exit /b 1
)

REM Check if emulator exists
if not exist "%ANDROID_SDK%\emulator\emulator.exe" (
    echo Emulator not found at: %ANDROID_SDK%\emulator\emulator.exe
    echo Please make sure the Android SDK is properly installed.
    exit /b 1
)

REM Create local.properties file
echo Creating local.properties file...
echo sdk.dir=%ANDROID_SDK% > android\local.properties
echo Created android\local.properties with SDK path

REM List available AVDs
echo.
echo Available Android Virtual Devices (AVDs):
"%ANDROID_SDK%\emulator\emulator.exe" -list-avds

REM Check if any AVDs are available
set /a count=0
for /f "tokens=*" %%a in ('"%ANDROID_SDK%\emulator\emulator.exe" -list-avds') do (
    set /a count+=1
    set last_avd=%%a
)

if %count%==0 (
    echo.
    echo No AVDs found. Please create an AVD using Android Studio.
    echo See android-emulator-guide.md for instructions.
    exit /b 1
)

REM If only one AVD is available, use it automatically
if %count%==1 (
    echo.
    echo Using the only available AVD: %last_avd%
    set AVD_NAME=%last_avd%
) else (
    echo.
    echo Please enter the name of the AVD you want to use:
    set /p AVD_NAME=
)

REM Start the emulator
echo.
echo Starting emulator with AVD: %AVD_NAME%
start "" "%ANDROID_SDK%\emulator\emulator.exe" -avd %AVD_NAME%

echo.
echo Emulator is starting in a new window.
echo Once the emulator is fully loaded, you can run your app with:
echo npm run android
echo.
echo Press any key to exit...
pause > nul
