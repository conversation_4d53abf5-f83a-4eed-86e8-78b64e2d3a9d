# Android Emulator Setup Guide

This guide will help you set up an Android emulator for your React Native project.

## Step 1: Install Android Studio

If you haven't already installed Android Studio, download and install it from the official website:
[Download Android Studio](https://developer.android.com/studio)

## Step 2: Set up Android SDK

1. Open Android Studio
2. Go to Tools > SDK Manager
3. In the SDK Platforms tab, select Android 14 (API Level 34) or the version you want to target
4. In the SDK Tools tab, make sure the following are installed:
   - Android SDK Build-Tools
   - Android Emulator
   - Android SDK Platform-Tools
   - Google Play services
5. Click "Apply" to install the selected components

## Step 3: Create an Android Virtual Device (AVD)

1. In Android Studio, go to Tools > Device Manager
2. Click on "Create Device"
3. Select a phone device (e.g., Pixel 5)
4. Select a system image (e.g., Android 14 with Google Play)
5. Give your AVD a name (e.g., "Pixel5_API34")
6. Click "Finish" to create the AVD

## Step 4: Configure your React Native project

1. Create a `local.properties` file in the `android` folder of your project with the following content:
   ```
   sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk
   ```
   Replace `YourUsername` with your Windows username and adjust the path if your SDK is installed elsewhere.

## Step 5: Start the emulator

You can start the emulator in one of these ways:

1. From Android Studio:
   - Go to Tools > Device Manager
   - Click the play button next to your AVD

2. From command line:
   ```
   C:\Users\<USER>\AppData\Local\Android\Sdk\emulator\emulator.exe -avd YourAVDName
   ```
   Replace `YourUsername` with your Windows username and `YourAVDName` with the name of your AVD.

## Step 6: Run your React Native app

Once the emulator is running, you can start your React Native app:

```
npm run android
```

## Troubleshooting

If you encounter the error "Error fetching your Android emulators! Make sure your path is correct", try the following:

1. Make sure the Android SDK path in your `local.properties` file is correct
2. Add the Android SDK to your PATH environment variable:
   - Right-click on "This PC" and select "Properties"
   - Click on "Advanced system settings"
   - Click on "Environment Variables"
   - Under "System variables", find the "Path" variable and click "Edit"
   - Add the following paths:
     ```
     C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools
     C:\Users\<USER>\AppData\Local\Android\Sdk\emulator
     ```
   - Replace `YourUsername` with your Windows username

3. Restart your terminal or command prompt after making these changes

4. Try running the emulator list command again:
   ```
   C:\Users\<USER>\AppData\Local\Android\Sdk\emulator\emulator.exe -list-avds
   ```

If you still encounter issues, make sure that:
- Android Studio is properly installed
- The Android SDK is installed and up to date
- You have created at least one AVD
- Your system has enough resources to run the emulator
