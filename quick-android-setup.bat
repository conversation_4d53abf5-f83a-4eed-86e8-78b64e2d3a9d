@echo off
echo ===================================================
echo Quick Android Setup Script
echo ===================================================

REM Find Android SDK
if exist "C:\Users\<USER>\AppData\Local\Android\Sdk" (
    set "ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk"
    echo Found Android SDK at: %ANDROID_HOME%
) else (
    echo Android SDK not found at default location
    echo Please install Android Studio first
    pause
    exit /b 1
)

REM Create local.properties file
echo Creating local.properties file...
if not exist android mkdir android
echo sdk.dir=%ANDROID_HOME% > android\local.properties
echo Created android\local.properties with SDK path: %ANDROID_HOME%

REM Check emulator
if exist "%ANDROID_HOME%\emulator\emulator.exe" (
    echo Found emulator at: %ANDROID_HOME%\emulator\emulator.exe
    echo Available AVDs:
    "%ANDROID_HOME%\emulator\emulator.exe" -list-avds
    echo.
    echo To start an emulator, use:
    echo "%ANDROID_HOME%\emulator\emulator.exe" -avd [AVD_NAME]
) else (
    echo Emulator not found
)

echo.
echo Setup complete! You can now run: npm run android
pause
