import axios from "axios";
import { BASE_URL } from "./config";
import { refresh_tokens } from "./authService";
import { Alert } from "react-native";
import { tokenStorage } from "@state/storage";

// For debugging network requests
axios.interceptors.request.use(request => {
  console.log('Starting Request', {
    url: request.url,
    method: request.method,
    data: request.data
  });
  return request;
});

axios.interceptors.response.use(response => {
  console.log('Response:', {
    url: response.config.url,
    status: response.status,
    data: response.data
  });
  return response;
}, error => {
  console.error('Response Error:', {
    url: error.config?.url,
    status: error.response?.status,
    data: error.response?.data,
    message: error.message
  });
  return Promise.reject(error);
});


export const appAxios = axios.create({
    baseURL: BASE_URL,
    timeout: 10000 // 10 second timeout
})

appAxios.interceptors.request.use(async config => {
    console.log(`appAxios Request: ${config.method?.toUpperCase()} ${config.url}`);
    const accessToken = tokenStorage.getString('accessToken')
    if (accessToken) {
        config.headers.Authorization = `Bearer ${accessToken}`
        console.log('Request includes authorization token');
    } else {
        console.log('No authorization token available');
    }
    return config
}, error => {
    console.error('Request configuration error:', error);
    return Promise.reject(error);
})

appAxios.interceptors.response.use(
    response => {
        console.log(`appAxios Response: ${response.status} for ${response.config.url}`);
        return response;
    },
    async error => {
        console.error(`appAxios Error: ${error.response?.status || 'Network Error'} for ${error.config?.url}`);

        // Handle 401 Unauthorized errors by refreshing the token
        if (error.response && error.response.status === 401) {
            console.log('Unauthorized error, attempting to refresh token');
            try {
                const newAccessToken = await refresh_tokens()
                if (newAccessToken) {
                    console.log('Token refreshed successfully, retrying request');
                    error.config.headers.Authorization = `Bearer ${newAccessToken}`
                    return axios(error.config)
                }
            } catch (refreshError) {
                console.error("ERROR REFRESHING TOKEN:", refreshError);
                // Let the error propagate to the caller
                return Promise.reject(error);
            }
        }

        // Log other error types
        if (error.response) {
            // Server responded with a status code outside of 2xx range
            const errorMessage = error.response.data?.message || 'Server error';
            console.error(`Server error ${error.response.status}: ${errorMessage}`);
        } else if (error.request) {
            // Request was made but no response received
            console.error('No response received:', error.request);
        } else {
            // Error in setting up the request
            console.error('Request setup error:', error.message);
        }

        // Return a rejected promise to propagate the error to the caller
        return Promise.reject(error);
    }
)