@echo off
echo ===================================================
echo Java 17 Installation Script for React Native
echo ===================================================
echo.
echo This script will help you install Java 17 (OpenJDK) which is required for React Native 0.77
echo.

REM Check if Java 17 is already installed
echo Checking for existing Java 17 installation...
if exist "C:\Program Files\Eclipse Adoptium\jdk-17*" (
    echo Java 17 (Eclipse Temurin) already found in Program Files.
    goto :configure
)

if exist "C:\Users\<USER>\AppData\Local\jdk-17*" (
    echo Java 17 already found in user directory.
    goto :configure
)

echo Java 17 not found. You need to install it manually.
echo.
echo Please follow these steps:
echo.
echo 1. Open your web browser and go to:
echo    https://adoptium.net/temurin/releases/?version=17
echo.
echo 2. Download the Windows x64 MSI installer for Java 17
echo    (Look for: OpenJDK 17 LTS - Windows x64 - JDK - .msi)
echo.
echo 3. Run the downloaded MSI file and install with default settings
echo.
echo 4. After installation, run this script again to configure the environment
echo.
echo Opening the download page for you...
start https://adoptium.net/temurin/releases/?version=17
echo.
pause
exit /b 1

:configure
echo.
echo Configuring Java 17 for React Native...

REM Find Java 17 installation
set "JAVA17_PATH="
if exist "C:\Program Files\Eclipse Adoptium\jdk-**********-hotspot" (
    set "JAVA17_PATH=C:\Program Files\Eclipse Adoptium\jdk-**********-hotspot"
) else (
    for /d %%i in ("C:\Program Files\Eclipse Adoptium\jdk-17*") do (
        set "JAVA17_PATH=%%i"
        goto :found
    )
    for /d %%i in ("C:\Users\<USER>\AppData\Local\jdk-17*") do (
        set "JAVA17_PATH=%%i"
        goto :found
    )
)

:found
if "%JAVA17_PATH%"=="" (
    echo Could not find Java 17 installation. Please install it first.
    pause
    exit /b 1
)

echo Found Java 17 at: %JAVA17_PATH%
echo.

REM Update gradle.properties for this project
echo Updating gradle.properties for React Native project...
if not exist "android\gradle.properties" (
    echo gradle.properties not found. Make sure you're in the React Native project root.
    pause
    exit /b 1
)

REM Backup existing gradle.properties
copy "android\gradle.properties" "android\gradle.properties.backup" >nul
echo Created backup: android\gradle.properties.backup

REM Add or update Java home in gradle.properties
echo org.gradle.java.home=%JAVA17_PATH% >> android\gradle.properties
echo Added Java 17 path to gradle.properties

echo.
echo ===================================================
echo Configuration complete!
echo.
echo Java 17 path: %JAVA17_PATH%
echo Updated: android\gradle.properties
echo.
echo You can now try running your React Native app:
echo npm run android
echo.
echo If you still get Java version errors, you may need to:
echo 1. Restart your terminal/IDE
echo 2. Set JAVA_HOME environment variable system-wide
echo ===================================================
pause
